/**
 * Utility để quản lý biến môi trường
 * Cung cấp các helper function để truy cập biến môi trường một cách an toàn
 */

/**
 * Kiểu dữ liệu cho các biến môi trường
 */
export interface EnvVariables {
  // API URLs
  apiUrl: string;
  recaptchaSiteKey: string;

  // Social login URLs
  googleLoginUrl: string;
  facebookLoginUrl: string;
  zaloLoginUrl: string;

  // Môi trường hiện tại
  isDevelopment: boolean;
  isLocal: boolean;
  isProduction: boolean;
  isStaging: boolean;
  isTesting: boolean;
  mode: string;
  environment: string;
}

/**
 * Utility để truy cập biến môi trường
 */
export const env: EnvVariables & {
  get: (key: string, defaultValue?: string) => string;
  getBoolean: (key: string, defaultValue?: boolean) => boolean;
  getNumber: (key: string, defaultValue?: number) => number;
} = {
  // API URLs
  apiUrl: (import.meta.env['VITE_API_URL'] as string) || '',
  recaptchaSiteKey: (import.meta.env['VITE_RECAPTCHA_SITE_KEY'] as string) || '',

  // Social login URLs
  googleLoginUrl: (import.meta.env['VITE_FULL_REDIRECT_GOOGLE_LOGIN'] as string) || '',
  facebookLoginUrl: (import.meta.env['VITE_FULL_REDIRECT_FACEBOOK_LOGIN'] as string) || '',
  zaloLoginUrl: (import.meta.env['VITE_FULL_REDIRECT_ZALO_LOGIN'] as string) || '',

  // Môi trường hiện tại
  isDevelopment: import.meta.env.DEV as boolean,
  isProduction: import.meta.env.PROD as boolean,
  isStaging: import.meta.env.MODE === 'staging',
  isTesting: import.meta.env.MODE === 'testing',
  isLocal: import.meta.env.MODE === 'localhost',
  mode: import.meta.env.MODE as string,
  environment: (import.meta.env['VITE_ENVIRONMENT'] as string) || (import.meta.env['MODE'] as string),

  /**
   * Lấy giá trị biến môi trường dưới dạng string
   * @param key Tên biến môi trường
   * @param defaultValue Giá trị mặc định nếu biến không tồn tại
   * @returns Giá trị biến môi trường hoặc giá trị mặc định
   */
  get: (key: string, defaultValue: string = ''): string => {
    const value = import.meta.env[key];
    return value !== undefined ? String(value) : defaultValue;
  },

  /**
   * Lấy giá trị biến môi trường dưới dạng boolean
   * @param key Tên biến môi trường
   * @param defaultValue Giá trị mặc định nếu biến không tồn tại
   * @returns Giá trị biến môi trường dưới dạng boolean
   */
  getBoolean: (key: string, defaultValue: boolean = false): boolean => {
    const value = import.meta.env[key];
    if (value === undefined) return defaultValue;
    return value === 'true' || value === '1' || value === true;
  },

  /**
   * Lấy giá trị biến môi trường dưới dạng number
   * @param key Tên biến môi trường
   * @param defaultValue Giá trị mặc định nếu biến không tồn tại hoặc không phải số
   * @returns Giá trị biến môi trường dưới dạng number
   */
  getNumber: (key: string, defaultValue: number = 0): number => {
    const value = import.meta.env[key];
    if (value === undefined) return defaultValue;
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
  },
};

export default env;
