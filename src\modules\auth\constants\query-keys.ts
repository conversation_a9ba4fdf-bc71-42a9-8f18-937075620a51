/**
 * <PERSON><PERSON><PERSON> query key cho module auth
 */
export const AUTH_QUERY_KEYS = {
  // Thông tin người dùng hiện tại
  CURRENT_USER: ['auth', 'currentUser'],

  // Đăng nhập
  LOGIN: ['auth', 'login'],

  // Đăng ký
  REGISTER: ['auth', 'register'],

  // Xác thực OTP
  VERIFY_OTP: ['auth', 'verifyOtp'],

  // Gửi lại OTP
  RESEND_OTP: ['auth', 'resendOtp'],

  // Quên mật khẩu
  FORGOT_PASSWORD: ['auth', 'forgotPassword'],

  // Xác thực quên mật khẩu
  VERIFY_FORGOT_PASSWORD: ['auth', 'verifyForgotPassword'],

  // Đặt lại mật khẩu
  RESET_PASSWORD: ['auth', 'resetPassword'],

  // Đăng nhập Google
  GOOGLE_LOGIN: ['auth', 'googleLogin'],

  // Đăng nhập Facebook
  FACEBOOK_LOGIN: ['auth', 'facebookLogin'],

  // URL xác thực Google
  GOOGLE_AUTH_URL: ['auth', 'googleAuthUrl'],

  // Xác thực hai lớp
  VERIFY_TWO_FACTOR: ['auth', 'verifyTwoFactor'],

  // URL xác thực Facebook
  FACEBOOK_AUTH_URL: ['auth', 'facebookAuthUrl'],

  // Đăng nhập Zalo
  ZALO_LOGIN: ['auth', 'zaloLogin'],

  // URL xác thực Zalo
  ZALO_AUTH_URL: ['auth', 'zaloAuthUrl'],
} as const;
