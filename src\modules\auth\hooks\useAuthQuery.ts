import { useMutation, useQuery } from '@tanstack/react-query';
import { RegisterFormValues } from '../schemas/auth.schema';
import { AUTH_QUERY_KEYS } from '../constants';
import {
  LoginRequest,
  GoogleAuthRequest,
  FacebookAuthRequest,
  ZaloAuthRequest,
  VerifyOtpRequest,
  ResendOtpRequest,
  VerifyForgotPasswordRequest,
  VerifyTwoFactorRequest,
} from '../types/auth.types';
import { AuthService } from '../services/auth.service';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';

/**
 * Hook để đăng nhập
 * Không xử lý onSuccess ở đây để tránh trùng lặp với xử lý trong component
 */
export const useLogin = () => {
  return useMutation({
    mutationFn: (data: LoginRequest) => AuthService.login(data),
    retry: 0, // Tắt retry cho API đăng nhập
  });
};

/**
 * Hook để đăng ký
 */
export const useRegister = () => {
  return useMutation({
    mutationFn: (data: RegisterFormValues & { recaptchaToken?: string }) =>
      AuthService.register({
        fullName: data.fullName,
        email: data.email,
        password: data.password,
        phoneNumber: data.phone || '', // Số điện thoại không có country code
        countryCode: data.countryCode, // Mã quốc gia riêng biệt
        recaptchaToken: data.recaptchaToken,
      }),
    retry: 0, // Tắt retry cho API đăng ký
  });
};

/**
 * Hook để lấy thông tin người dùng hiện tại
 */
export const useCurrentUser = () => {
  const { getToken } = useAuthCommon();

  return useQuery({
    queryKey: AUTH_QUERY_KEYS.CURRENT_USER,
    queryFn: () => AuthService.getCurrentUser(),
    // Không tự động gọi API nếu không có token
    enabled: !!localStorage.getItem('token') || !!getToken(),
    select: data => data.result,
  });
};

/**
 * Hook để lấy URL xác thực Google
 */
export const useGoogleAuthUrl = () => {
  return useMutation({
    mutationKey: AUTH_QUERY_KEYS.GOOGLE_AUTH_URL,
    mutationFn: (redirectUri?: string) => AuthService.getGoogleAuthUrl(redirectUri),
    retry: 0, // Tắt retry cho API lấy URL xác thực Google
  });
};

/**
 * Hook để đăng nhập bằng Google
 */
export const useGoogleLogin = () => {
  return useMutation({
    mutationKey: AUTH_QUERY_KEYS.GOOGLE_LOGIN,
    mutationFn: (data: GoogleAuthRequest) => AuthService.loginWithGoogle(data),
    retry: 0, // Tắt retry cho API đăng nhập bằng Google
  });
};

/**
 * Hook để lấy URL xác thực Facebook
 */
export const useFacebookAuthUrl = () => {
  return useMutation({
    mutationKey: AUTH_QUERY_KEYS.FACEBOOK_AUTH_URL,
    mutationFn: (redirectUri?: string) => AuthService.getFacebookAuthUrl(redirectUri),
    retry: 0, // Tắt retry cho API lấy URL xác thực Facebook
  });
};

/**
 * Hook để đăng nhập bằng Facebook
 */
export const useFacebookLogin = () => {
  return useMutation({
    mutationKey: AUTH_QUERY_KEYS.FACEBOOK_LOGIN,
    mutationFn: (data: FacebookAuthRequest) => AuthService.loginWithFacebook(data),
    retry: 0, // Tắt retry cho API đăng nhập bằng Facebook
  });
};

/**
 * Hook để lấy URL xác thực Zalo
 */
export const useZaloAuthUrl = () => {
  return useMutation({
    mutationKey: AUTH_QUERY_KEYS.ZALO_AUTH_URL,
    mutationFn: (redirectUri?: string) => AuthService.getZaloAuthUrl(redirectUri),
    retry: 0, // Tắt retry cho API lấy URL xác thực Zalo
  });
};

/**
 * Hook để đăng nhập bằng Zalo
 */
export const useZaloLogin = () => {
  return useMutation({
    mutationKey: AUTH_QUERY_KEYS.ZALO_LOGIN,
    mutationFn: (data: ZaloAuthRequest) => AuthService.loginWithZalo(data),
    retry: 0, // Tắt retry cho API đăng nhập bằng Zalo
  });
};

/**
 * Hook để đăng xuất
 * Không xử lý onSuccess/onError ở đây để tránh trùng lặp với xử lý trong component
 */
export const useLogout = () => {
  return useMutation({
    mutationFn: () => AuthService.logout(),
    retry: 0, // Tắt retry cho API đăng xuất
  });
};

/**
 * Hook để gửi yêu cầu quên mật khẩu
 */
export const useForgotPassword = () => {
  return useMutation({
    mutationFn: (data: { email: string }) => AuthService.forgotPassword(data),
    retry: 0, // Tắt retry cho API quên mật khẩu
  });
};

/**
 * Hook để đặt lại mật khẩu
 */
export const useResetPassword = () => {
  return useMutation({
    mutationFn: (data: { token: string; password: string; confirmPassword: string }) => {
      // Chuyển đổi dữ liệu để phù hợp với ResetPasswordRequest
      return AuthService.resetPassword({
        newPassword: data.password,
        changePasswordToken: data.token,
      });
    },
    retry: 0, // Tắt retry cho API đặt lại mật khẩu
  });
};

/**
 * Hook để xác thực OTP
 */
export const useVerifyOtp = () => {
  return useMutation({
    mutationFn: (data: VerifyOtpRequest) => AuthService.verifyOtp(data),
    retry: 0, // Tắt retry cho API xác thực OTP
  });
};

/**
 * Hook để gửi lại OTP
 */
export const useResendOtp = () => {
  return useMutation({
    mutationFn: (data: ResendOtpRequest) => AuthService.resendOtp(data),
    retry: 0, // Tắt retry cho API gửi lại OTP
  });
};

/**
 * Hook để xác thực OTP quên mật khẩu
 */
export const useVerifyForgotPassword = () => {
  return useMutation({
    mutationFn: (data: VerifyForgotPasswordRequest) => AuthService.verifyForgotPassword(data),
    retry: 0, // Tắt retry cho API xác thực OTP quên mật khẩu
  });
};

/**
 * Hook để xác thực hai lớp (2FA)
 */
export const useVerifyTwoFactor = () => {
  return useMutation({
    mutationFn: (data: VerifyTwoFactorRequest) => AuthService.verifyTwoFactor(data),
    mutationKey: AUTH_QUERY_KEYS.VERIFY_TWO_FACTOR,
    retry: 0, // Tắt retry cho API xác thực hai lớp
  });
};
